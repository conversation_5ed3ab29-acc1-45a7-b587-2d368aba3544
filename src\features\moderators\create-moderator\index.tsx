"use client";

import { Main } from "@/components/layout/main";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { zodResolver } from "@hookform/resolvers/zod";
import { Separator } from "@radix-ui/react-separator";
import { useForm } from "react-hook-form";
import { moderatorCreateSchema, ModeratorFormValues } from "../data/schema";
import { addModerator, getModDetails, updateModerator } from "../api";

import { useEffect } from "react"
import { X } from "lucide-react"
import { Command, CommandGroup, CommandItem, CommandInput, CommandEmpty } from "@/components/ui/command"
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover"

import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { useNavigate, useParams } from "@tanstack/react-router";
import { toast } from "sonner";
import { END_POINTS } from "@/features/members/utils/constant";

const countries = [
    { label: "United States", value: "usa" },
    { label: "United Kingdom", value: "uk" },
    { label: "Canada", value: "canada" },
    { label: "Australia", value: "australia" },
    { label: "Germany", value: "germany" },
    { label: "France", value: "france" },
    { label: "Spain", value: "spain" },
    { label: "Italy", value: "italy" },
    { label: "Japan", value: "japan" },
    { label: "India", value: "india" },
]

export default function CreateModerator() {
    const navigate = useNavigate()
    const { mutateAsync: addModeratorMutation } = addModerator()
    const { mutateAsync: updateModeratorMutation } = updateModerator()
    const { moderatorId } = useParams({ strict: false })
    const { data = {} } = getModDetails(moderatorId)

    const form = useForm<ModeratorFormValues>({
        resolver: zodResolver(moderatorCreateSchema),
        defaultValues: {
            isTrigger: false,
            translatorAccess: false,
            messageAccess: "all",
            paymentCurrency: "EUR",
            paymentMethod: data?.user?.user_profile?.paymentMethod || "none",
            moderatorType: data?.user?.user_profile?.moderatorType || "",
        },
    });

    const { control, handleSubmit, reset, setValue, watch } = form;
    const paymentMethod = watch("paymentMethod");


    useEffect(() => {
        if (data?.user) {
            reset(data.user.user_profile)

            // manually fix dropdown issue if needed
            if (data.user.user_profile.paymentCurrency) {
                setValue('paymentCurrency', data.user.user_profile.paymentCurrency)
                setValue('name', data.user.name)
                setValue('email', data.user.email)

                // payment fields
                setValue('swiftBicCode', data.user.swiftBicCode || "")
                setValue('accountNumber', data.user.accountNumber || "")
                setValue('beneficiaryAccountAddress', data.user.beneficiaryAccountAddress || "")
                setValue('iban', data.user.iban || "")
                setValue('bicSwift', data.user.bicSwift || "")
                setValue('bankName', data.user.bankName || "")
                setValue('bankAddress', data.user.bankAddress || "")
                setValue('beneficiaryAddress', data.user.beneficiaryAddress || "")
                setValue('intermediaryBankSwift', data.user.intermediaryBankSwift || "")
                setValue('purposeOfPayment', data.user.purposeOfPayment || "")
                setValue('beneficiaryAccountName', data.user.beneficiaryAccountName || "")
            }
        }
    }, [data, reset, setValue])

    const onSubmit = async (values: ModeratorFormValues) => {

        const payload: any = {
            ...values,
            name: values.name.trim(),
            nickname: values.nickname.trim(),
            minimumPayout: Number(values.minimumPayout),
            nativeCommission: Number(values.nativeCommission),
            hybridCommission: Number(values.hybridCommission),
            reLobbyCommission: Number(values.reLobbyCommission),
        }

        // Only include payment fields if payment method is not "none"
        if (values.paymentMethod !== "none") {
            payload.beneficiaryAccountName = values.beneficiaryAccountName?.trim() || "";
            payload.beneficiaryCity = values.beneficiaryCity?.trim() || "";
            payload.beneficiaryBankAddress = values.beneficiaryBankAddress?.trim() || "";

            // Include payment method specific fields
            if (values.paymentMethod === "sepa") {
                payload.iban = values.iban || "";
                payload.bicSwift = values.bicSwift || "";
                payload.beneficiaryAccountAddress = values.beneficiaryAccountAddress || "";
            } else if (values.paymentMethod === "swift") {
                payload.accountNumber = values.accountNumber || "";
                payload.swiftBicCode = values.swiftBicCode || "";
                payload.bankName = values.bankName || "";
                payload.bankAddress = values.bankAddress || "";
                payload.beneficiaryAddress = values.beneficiaryAddress || "";
                payload.intermediaryBankSwift = values.intermediaryBankSwift || "";
                payload.purposeOfPayment = values.purposeOfPayment || "";
            }
        } else {
            delete payload.beneficiaryAccountName;
            delete payload.beneficiaryCity;
            delete payload.beneficiaryBankAddress;
            delete payload.iban;
            delete payload.bicSwift;
            delete payload.beneficiaryAccountAddress;
            delete payload.accountNumber;
            delete payload.swiftBicCode;
            delete payload.bankName;
            delete payload.bankAddress;
            delete payload.beneficiaryAddress;
            delete payload.intermediaryBankSwift;
            delete payload.purposeOfPayment;
        }

        if (typeof moderatorId === "string") {
            const { email, ...restPayload }: any = payload
            const response: any = await updateModeratorMutation({ ...restPayload, id: data?.user?.id })
            if (response?.success) {
                toast.success("Moderator Profile has been updated!")
            }

        } else {

            const response: any = await addModeratorMutation(payload)
            if (response?.success) {
                navigate({ to: END_POINTS.MODERATORS });
            }
        }
    };

    console.log(form.getValues())
    return (
        <Main>
            <div className="space-y-0.5">
                <h1 className="text-2xl font-bold tracking-tight md:text-3xl">
                    {typeof moderatorId === "string" ? "Update Moderator" : "Create Moderator"}
                </h1>
                <p className="text-muted-foreground">
                    {typeof moderatorId === "string" ? "Update moderator account." : "Create a new moderator account."}
                </p>
            </div>
            <Separator className="my-4 lg:my-6" />
            <div className="flex flex-1 flex-col space-y-2 overflow-hidden md:space-y-2 xl:flex-row lg:space-y-0 lg:space-x-12">
                <div className="flex w-full overflow-y-hidden p-1">
                    <Form {...form}>
                        <form
                            onSubmit={handleSubmit(onSubmit)}
                            // className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full"
                            className="flex flex-col xl:flex-row gap-4 w-full"
                        >
                            <div className="flex flex-1 basis-[50%] flex-col gap-4">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Primary Details</CardTitle>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <FormField
                                            name="name"
                                            control={control}
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel className="form-label">Name</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            name="nickname"
                                            control={control}
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel className="form-label">Nick Name</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            name="email"
                                            control={control}
                                            disabled={typeof moderatorId === "string"}
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel className="form-label">Email</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            name="phone"
                                            control={control}
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel className="form-label">Phone</FormLabel>
                                                    <FormControl>
                                                        <Input type="number" {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            name="nativeCommission"
                                            control={control}
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel className="form-label">Native Commission per message(EUR)</FormLabel>
                                                    <FormControl>
                                                        <Input type="number" {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            name="hybridCommission"
                                            control={control}
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel className="form-label">Hybrid Commission per message(EUR) </FormLabel>
                                                    <FormControl>
                                                        <Input type="number" {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            name="reLobbyCommission"
                                            control={control}
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel className="form-label">Relobby Commission per message(EUR) </FormLabel>
                                                    <FormControl>
                                                        <Input type="number" {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </CardContent>
                                </Card>


                                <Card>
                                    <CardHeader>
                                        <CardTitle>Account Settings</CardTitle>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <FormField
                                            name="minimumPayout"
                                            control={control}
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel className="form-label">Minimum Payout</FormLabel>
                                                    <FormControl>
                                                        <Input type="number" {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            name="paymentCurrency"
                                            control={control}
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel className="form-label">Payment Currency</FormLabel>
                                                    <FormControl>
                                                        <Select onValueChange={field.onChange} value={field.value}>
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Payment Currency" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                <SelectItem disabled value="_">Select Payment Currency</SelectItem>
                                                                <SelectItem value="EUR">EUR</SelectItem>
                                                            </SelectContent>
                                                        </Select>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            name="paymentMethod"
                                            control={control}
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel className="form-label">Payment Method</FormLabel>
                                                    <FormControl>
                                                        <Select onValueChange={field.onChange} value={field.value}>
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Payment Method" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                <SelectItem disabled value="_">Select Payment Method</SelectItem>
                                                                <SelectItem value="none">None</SelectItem>
                                                                <SelectItem value="swift">SWIFT</SelectItem>
                                                                <SelectItem value="sepa">SEPA/IBAN</SelectItem>
                                                            </SelectContent>
                                                        </Select>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                    </CardContent>
                                </Card>
                            </div>
                            <div className="flex flex-1 basis-[50%] flex-col gap-4">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Moderator Role & Access</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        {/* First Row: Is Trigger and Translator Access - 50-50 split */}
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <FormField
                                                name="isTrigger"
                                                control={control}
                                                render={({ field }: any) => (
                                                    <FormItem>
                                                        <FormLabel>Is Trigger?</FormLabel>
                                                        <FormControl>
                                                            <RadioGroup
                                                                onValueChange={field.onChange}
                                                                value={field.value}
                                                            >
                                                                <div className="flex gap-2">
                                                                    <div className="flex items-center gap-2 border rounded-md px-3 py-2">
                                                                        <RadioGroupItem value={true} id="trigger-yes" />
                                                                        <label htmlFor="trigger-yes">Yes</label>
                                                                    </div>
                                                                    <div className="flex items-center gap-2 border rounded-md px-3 py-2">
                                                                        <RadioGroupItem value={false} id="trigger-no" />
                                                                        <label htmlFor="trigger-no">No</label>
                                                                    </div>
                                                                </div>
                                                            </RadioGroup>
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                            <FormField
                                                name="translatorAccess"
                                                control={control}
                                                render={({ field }: any) => (
                                                    <FormItem>
                                                        <FormLabel>Moderator Translator Access</FormLabel>
                                                        <FormControl>
                                                            <RadioGroup
                                                                onValueChange={field.onChange}
                                                                value={field.value}
                                                            >
                                                                <div className="flex gap-2">
                                                                    <div className="flex items-center gap-2 border rounded-md px-3 py-2">
                                                                        <RadioGroupItem value={true} id="translator-yes" />
                                                                        <label htmlFor="translator-yes">Yes</label>
                                                                    </div>
                                                                    <div className="flex items-center gap-2 border rounded-md px-3 py-2">
                                                                        <RadioGroupItem value={false} id="translator-no" />
                                                                        <label htmlFor="translator-no">No</label>
                                                                    </div>
                                                                </div>
                                                            </RadioGroup>
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </div>
                                        <FormField
                                            name="moderatorType"
                                            control={control}
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel className="form-label">Moderator Type</FormLabel>
                                                    <FormControl>
                                                        <Select onValueChange={field.onChange} value={field.value}>
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Select moderator type" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                <SelectItem value="WL Moderator">WL Moderator</SelectItem>
                                                                <SelectItem value="Default Moderator">Default Moderator</SelectItem>
                                                            </SelectContent>
                                                        </Select>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={control}
                                            name="countriesAssignNative"
                                            render={({ field }) => {
                                                const selectedValues: string[] = field.value || []

                                                const handleRemove = (value: string) => {
                                                    const updated = selectedValues.filter((v) => v !== value)
                                                    field.onChange(updated)
                                                }

                                                const toggleValue = (value: string) => {
                                                    const updated = selectedValues.includes(value)
                                                        ? selectedValues.filter((v) => v !== value)
                                                        : [...selectedValues, value]
                                                    field.onChange(updated)
                                                }

                                                return (
                                                    <FormItem>
                                                        <FormLabel className="form-label">Countries Assign as Native</FormLabel>
                                                        <Popover>
                                                            <PopoverTrigger asChild>
                                                                <FormControl>
                                                                    <div
                                                                        className={cn(
                                                                            "flex min-h-[40px] w-full flex-wrap items-center gap-1 rounded-md border border-input bg-background px-3 py-2 text-sm shadow-sm",
                                                                            !selectedValues.length && "text-muted-foreground"
                                                                        )}
                                                                    >
                                                                        {selectedValues.length === 0 && <span>Select countries</span>}
                                                                        {/* {selectedValues.slice(0, 2).map((val) => { */}
                                                                        {selectedValues.map((val) => {
                                                                            const label = countries.find((c) => c.value === val)?.label ?? val
                                                                            return (
                                                                                <div className="flex gap-1 items-center bg-muted rounded p-1">
                                                                                    <Badge
                                                                                        key={val}
                                                                                        className="cursor-default border-0 p-0 bg-transparent text-foreground"
                                                                                        onClick={(e) => e.stopPropagation()}
                                                                                    >
                                                                                        {label}
                                                                                    </Badge>

                                                                                    <X
                                                                                        className="h-3 w-3 cursor-pointer"
                                                                                        onClick={(e) => {
                                                                                            e.stopPropagation()
                                                                                            handleRemove(val)
                                                                                        }}
                                                                                    />
                                                                                </div>
                                                                            )
                                                                        })}
                                                                        {/* {selectedValues.length > 2 && (
                                                                            <span className="ml-2 text-muted-foreground">...</span>
                                                                        )} */}
                                                                    </div>
                                                                </FormControl>
                                                            </PopoverTrigger>
                                                            <PopoverContent className="w-full p-0">
                                                                <Command>
                                                                    <CommandInput placeholder="Search countries..." />
                                                                    <CommandEmpty>No country found.</CommandEmpty>
                                                                    <CommandGroup>
                                                                        {countries.map((country) => (
                                                                            <CommandItem
                                                                                key={country.value}
                                                                                onSelect={() => toggleValue(country.value)}
                                                                                className="cursor-pointer"
                                                                            >
                                                                                <span>{country.label}</span>
                                                                                {selectedValues.includes(country.value) && (
                                                                                    <span className="ml-auto text-primary">✓</span>
                                                                                )}
                                                                            </CommandItem>
                                                                        ))}
                                                                    </CommandGroup>
                                                                </Command>
                                                            </PopoverContent>
                                                        </Popover>
                                                        <FormMessage />
                                                    </FormItem>
                                                )
                                            }}
                                        />

                                        <FormField
                                            control={control}
                                            name="countriesAssignHybrid"
                                            render={({ field }) => {
                                                const selectedValues: string[] = field.value || []

                                                const toggleValue = (value: string) => {
                                                    if (selectedValues.includes(value)) {
                                                        field.onChange(selectedValues.filter((v) => v !== value))
                                                    } else {
                                                        field.onChange([...selectedValues, value])
                                                    }
                                                }

                                                return (
                                                    <FormItem>
                                                        <FormLabel className="form-label">Countries Assign as Hybrid</FormLabel>
                                                        <Popover>
                                                            <PopoverTrigger asChild>
                                                                <FormControl>
                                                                    <div
                                                                        className={cn(
                                                                            "flex min-h-[40px] w-full flex-wrap items-center gap-1 rounded-md border border-input bg-background px-3 py-2 text-sm shadow-sm",
                                                                            !selectedValues.length && "text-muted-foreground"
                                                                        )}
                                                                    >
                                                                        {selectedValues.length === 0 && <span>Select countries</span>}
                                                                        {selectedValues.map((val) => {
                                                                            const label = countries.find((c) => c.value === val)?.label ?? val
                                                                            return (
                                                                                <div className="flex gap-1 items-center bg-[#f5f5f5] rounded p-1">
                                                                                    <Badge key={val} className="cursor-default border-0 p-0 bg-transparent text-black">
                                                                                        {label}

                                                                                    </Badge>
                                                                                    <X
                                                                                        className="h-3 w-3 cursor-pointer"
                                                                                        onClick={(e) => {
                                                                                            e.stopPropagation()
                                                                                            toggleValue(val)
                                                                                        }}
                                                                                    />
                                                                                </div>
                                                                            )
                                                                        })}
                                                                    </div>
                                                                </FormControl>
                                                            </PopoverTrigger>
                                                            <PopoverContent className="w-full p-0">
                                                                <Command>
                                                                    <CommandInput placeholder="Search countries..." />
                                                                    <CommandEmpty>No country found.</CommandEmpty>
                                                                    <CommandGroup>
                                                                        {countries.map((country) => (
                                                                            <CommandItem
                                                                                key={country.value}
                                                                                onSelect={() => toggleValue(country.value)}
                                                                                className="cursor-pointer"
                                                                            >
                                                                                <span>{country.label}</span>
                                                                                {selectedValues.includes(country.value) && (
                                                                                    <span className="ml-auto text-primary">✓</span>
                                                                                )}
                                                                            </CommandItem>
                                                                        ))}
                                                                    </CommandGroup>
                                                                </Command>
                                                            </PopoverContent>
                                                        </Popover>
                                                        <FormMessage />
                                                    </FormItem>
                                                )
                                            }}
                                        />

                                        <FormField
                                            name="messageAccess"
                                            control={control}
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Moderator Message Access</FormLabel>
                                                    <FormControl>
                                                        <RadioGroup
                                                            onValueChange={field.onChange}
                                                            value={field.value}
                                                        >
                                                            <div className="flex gap-2 flex-wrap">
                                                                <div className="flex items-center gap-2 border rounded-md px-3 py-2">
                                                                    <RadioGroupItem value="all" id="message-all" />
                                                                    <label htmlFor="message-all">All</label>
                                                                </div>
                                                                <div className="flex items-center gap-2 border rounded-md px-3 py-2">
                                                                    <RadioGroupItem
                                                                        value="except-relobby"
                                                                        id="message-except-relobby"
                                                                    />
                                                                    <label htmlFor="message-except-relobby">
                                                                        All except re-lobby
                                                                    </label>
                                                                </div>
                                                                <div className="flex items-center gap-2 border rounded-md px-3 py-2">
                                                                    <RadioGroupItem
                                                                        value="only-relobby"
                                                                        id="message-only-relobby"
                                                                    />
                                                                    <label htmlFor="message-only-relobby">
                                                                        Only re-lobby
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </RadioGroup>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </CardContent>
                                </Card>



                                {paymentMethod !== "none" && (
                                    <Card>
                                        <CardHeader>
                                            <CardTitle>Payment Information</CardTitle>
                                        </CardHeader>
                                        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            {/* Common fields for both payment methods */}
                                            <FormField
                                                name="beneficiaryAccountName"
                                                control={control}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel className="form-label">Beneficiary Account Name *</FormLabel>
                                                        <FormControl>
                                                            <Input {...field} />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />

                                            {/* SEPA/IBAN Payment Method Fields */}
                                            {paymentMethod === "sepa" && (
                                                <>
                                                    <FormField
                                                        name="iban"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <FormItem>
                                                                <FormLabel className="form-label">IBAN (International Bank Account Number) *</FormLabel>
                                                                <FormControl>
                                                                    <Input {...field} placeholder="e.g., **********************" />
                                                                </FormControl>
                                                                <FormMessage />
                                                            </FormItem>
                                                        )}
                                                    />
                                                    <FormField
                                                        name="bicSwift"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <FormItem>
                                                                <FormLabel className="form-label">BIC/SWIFT (Optional)</FormLabel>
                                                                <FormControl>
                                                                    <Input {...field} placeholder="e.g., COBADEFFXXX" />
                                                                </FormControl>
                                                                <FormMessage />
                                                            </FormItem>
                                                        )}
                                                    />
                                                    <FormField
                                                        name="beneficiaryAccountAddress"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <FormItem>
                                                                <FormLabel className="form-label">Address (Optional)</FormLabel>
                                                                <FormControl>
                                                                    <Input {...field} placeholder="Street, City, Postal Code" />
                                                                </FormControl>
                                                                <FormMessage />
                                                            </FormItem>
                                                        )}
                                                    />
                                                    <div className="md:col-span-2">
                                                        <p className="text-sm text-muted-foreground">
                                                            <strong>Required for SEPA:</strong> IBAN, Account Holder's Name (above)<br />
                                                            <strong>Optional:</strong> BIC/SWIFT, Address
                                                        </p>
                                                    </div>
                                                </>
                                            )}

                                            {/* SWIFT Payment Method Fields */}
                                            {paymentMethod === "swift" && (
                                                <>
                                                    <FormField
                                                        name="accountNumber"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <FormItem>
                                                                <FormLabel className="form-label">Bank Account Number *</FormLabel>
                                                                <FormControl>
                                                                    <Input {...field} />
                                                                </FormControl>
                                                                <FormMessage />
                                                            </FormItem>
                                                        )}
                                                    />
                                                    <FormField
                                                        name="swiftBicCode"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <FormItem>
                                                                <FormLabel className="form-label">SWIFT/BIC Code *</FormLabel>
                                                                <FormControl>
                                                                    <Input {...field} placeholder="e.g., CHASUS33XXX" />
                                                                </FormControl>
                                                                <FormMessage />
                                                            </FormItem>
                                                        )}
                                                    />
                                                    <FormField
                                                        name="bankName"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <FormItem>
                                                                <FormLabel className="form-label">Bank Name *</FormLabel>
                                                                <FormControl>
                                                                    <Input {...field} placeholder="e.g., Chase Bank" />
                                                                </FormControl>
                                                                <FormMessage />
                                                            </FormItem>
                                                        )}
                                                    />
                                                    <FormField
                                                        name="bankAddress"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <FormItem>
                                                                <FormLabel className="form-label">Bank Address *</FormLabel>
                                                                <FormControl>
                                                                    <Input {...field} placeholder="Bank's city and country (or full address)" />
                                                                </FormControl>
                                                                <FormMessage />
                                                            </FormItem>
                                                        )}
                                                    />
                                                    <FormField
                                                        name="beneficiaryAddress"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <FormItem>
                                                                <FormLabel className="form-label">Beneficiary Address *</FormLabel>
                                                                <FormControl>
                                                                    <Input {...field} placeholder="Name, street, city, postal code, country" />
                                                                </FormControl>
                                                                <FormMessage />
                                                            </FormItem>
                                                        )}
                                                    />
                                                    <FormField
                                                        name="intermediaryBankSwift"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <FormItem>
                                                                <FormLabel className="form-label">Intermediary Bank SWIFT/BIC (Optional)</FormLabel>
                                                                <FormControl>
                                                                    <Input {...field} placeholder="If required by your bank" />
                                                                </FormControl>
                                                                <FormMessage />
                                                            </FormItem>
                                                        )}
                                                    />
                                                    <FormField
                                                        name="purposeOfPayment"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <FormItem>
                                                                <FormLabel className="form-label">Purpose of Payment (Optional)</FormLabel>
                                                                <FormControl>
                                                                    <Input {...field} placeholder="e.g., Service payment" />
                                                                </FormControl>
                                                                <FormMessage />
                                                            </FormItem>
                                                        )}
                                                    />
                                                    {/* <div className="md:col-span-2">
                                                    <p className="text-sm text-muted-foreground">
                                                        <strong>Required for SWIFT:</strong> Account Holder's Name (above), Bank Account Number, SWIFT/BIC Code, Bank Name, Bank Address, Beneficiary Address<br />
                                                        <strong>Optional:</strong> Intermediary Bank SWIFT/BIC, Purpose of Payment
                                                    </p>
                                                </div> */}
                                                </>
                                            )}
                                        </CardContent>
                                    </Card>
                                )}
                                <div className="md:col-span-2 flex mt-4 justify-end gap-4">

                                    <Button onClick={() => {
                                        navigate({ to: END_POINTS.MODERATORS })
                                    }} variant={"secondary"} style={{ cursor: 'pointer' }}>
                                        Cancel
                                    </Button>
                                    <Button style={{ cursor: 'pointer' }} type="submit">
                                        {typeof moderatorId === "string" ? "Update" : "Save"}
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </Form>
                </div>
            </div>
        </Main>
    );
}

