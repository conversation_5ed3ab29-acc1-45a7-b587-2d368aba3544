@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(0.92 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);

  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.95 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.light .card-bg {
  background-color: #6d6d6d;
}
.dark .card-bg {
  background-color: rgb(38, 38, 38);
}

.light .card-txt {
  color: rgb(229, 237, 244);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.97 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
  --card-bg: oklch(0.556 0 0);
}

form label {
  color: #6b6b6b;
  font-weight: 600;
}
.form-label {
}
.dark h2 {
  color: var(--foreground);
}

input {
  box-shadow: none;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Responsive scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thumb-gray-300 {
    scrollbar-color: rgb(209 213 219) transparent;
  }

  .dark .scrollbar-thumb-gray-600 {
    scrollbar-color: rgb(75 85 99) transparent;
  }

  /* Webkit scrollbar styles for better browser support */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(209 213 219);
    border-radius: 3px;
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(75 85 99);
  }

  /* Mobile-first responsive text sizes */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-sm sm:text-base lg:text-lg;
  }

  .text-responsive-lg {
    @apply text-base sm:text-lg lg:text-xl;
  }

  .text-responsive-xl {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  /* Responsive spacing utilities */
  .space-responsive-sm > * + * {
    @apply mt-2 sm:mt-3;
  }

  .space-responsive-md > * + * {
    @apply mt-3 sm:mt-4 lg:mt-6;
  }

  .space-responsive-lg > * + * {
    @apply mt-4 sm:mt-6 lg:mt-8;
  }

  /* Responsive padding utilities */
  .p-responsive-sm {
    @apply p-2 sm:p-3 lg:p-4;
  }

  .p-responsive-md {
    @apply p-3 sm:p-4 lg:p-6;
  }

  .p-responsive-lg {
    @apply p-4 sm:p-6 lg:p-8;
  }

  /* Mobile-optimized touch targets */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] sm:min-h-[40px] sm:min-w-[40px];
  }
}
button:focus-visible {
  box-shadow: none;
}
table {
  border-radius: 20px;
}

table thead tr th {
  background-color: #171717;
  color: #fff;
}

.light table tbody tr td {
  background-color: #fff;
}

.dark table tbody tr td {
  background-color: #000;
}

/* theme-tabs.css */

.light #announcement_tab {
  background: #fff;
  border-radius: 8px;
}

.dark #announcement_tab {
  background: #1a1a1a;
  border-radius: 8px;
}

/* theme-cards.css */

.light .themed-card {
  background: #ffffff;
  border-radius: 8px;
}

.dark .themed-card {
  background: #1a1a1a;
  border-radius: 8px;
}

.light .status-items {
  background: #ffffff;
  color: #000000;
}

.dark .status-items {
  background: #1a1a1a;
  color: #ebebeb;
}

.light .status-badge {
  background: #000;
  color: #fff;
}

.dark .status-badge {
  background: #ebebeb;
  color: #000;
}

.light .border-line {
  border-color: #1a1a1a;
}

.dark .border-line {
  border-color: #ebebeb;
}
