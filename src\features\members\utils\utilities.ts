export function formatDateToReadable(dateString: string): string {
    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = {
        month: "short",
        day: "numeric",
        year: "numeric",
    };
    return date.toLocaleDateString("en-US", options);
}

export const S3_BASE_URL = import.meta.env.VITE_S3_BASE_URL;

export function toTitleCase(str: any) {
    return str?.replace(/\w\S*/g, (txt: any) =>
        txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase()
    );
}

export function formatTimestampToLocal(timestamp: any) {
    const date = new Date(timestamp);
    const options: any = {
        // weekday: 'short',     // e.g., "Thu"
        year: 'numeric',      // e.g., "2025"
        month: 'short',        // e.g., "July"
        day: 'numeric',       // e.g., "3"
        hour: '2-digit',      // e.g., "04 PM"
        minute: '2-digit',
        second: '2-digit',
        hour12: false,         // for 12-hour format
        // timeZoneName: 'short' // e.g., "IST"
    };

    return date.toLocaleString('en-IN', options);
}

export function formatDateToYyyyDdMm(dateString: any) {
    const date = new Date(dateString);

    const yyyy = date.getFullYear();
    const dd = String(date.getDate()).padStart(2, '0');
    const mm = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed

    return `${yyyy}-${dd}-${mm}`;
}

export function calculateLoginSession(loginTime: any, logoutTime: any) {
    // Parse the input timestamps
    const loginDate: any = new Date(loginTime);
    const logoutDate: any = new Date(logoutTime);

    // Calculate the difference in milliseconds
    const diffMs = logoutDate - loginDate;

    // Convert to total seconds
    let totalSeconds = Math.floor(diffMs / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    totalSeconds %= 3600;
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;

    // Format to "HH:MM:SS"
    const pad = (num: any) => String(num).padStart(2, '0');
    return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`;
}



// Utility for image file validation

export const ACCEPTED_IMAGE_TYPES = [
    'image/png',
    'image/jpeg',
    'image/jpg',
];

export const ACCEPTED_IMAGE_EXTENSIONS = ['.png', '.jpg', '.jpeg'];

export const MAX_IMAGE_SIZE_MB = 20;
export const MAX_IMAGE_SIZE_BYTES = MAX_IMAGE_SIZE_MB * 1024 * 1024;

export function validateImageFile(file: File): string | null {
    if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
        return 'Only PNG and JPG images are allowed.';
    }
    if (file.size > MAX_IMAGE_SIZE_BYTES) {
        return `Image size must be less than ${MAX_IMAGE_SIZE_MB}MB.`;
    }
    return null;
}